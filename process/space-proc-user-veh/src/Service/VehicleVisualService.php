<?php

namespace App\Service;

use App\Helper\LcdvsProvider;
use App\Trait\LoggerTrait;

class VehicleVisualService
{
    use LoggerTrait;

    /**
     * TODO: Migrate to MongoDB ODM - temporarily disabled MongoAtlasQueryService dependency
     */
    public function __construct(
        private array $visualSettings
    ) {
    }

    /**
     * get visual vehicle from BO (v3D or default image..)
     * TODO: Migrate to MongoDB ODM - temporarily returning default visual settings
     */
    public function getVisualVehicle(?string $lcdv, string $brand): array
    {
        $this->logger->info('Get LVH Label - temporarily using default visual settings');

        // TODO: Migrate this MongoDB aggregation query to ODM
        // Temporarily return default visual settings
        return $this->visualSettings;

        /* DISABLED - TODO: Migrate to ODM
        $projection = [
            'visualSettings' => 1
        ];

        $response = $this->mongoAtlasQueryService->aggregate(
            'vehicleLabel',
            [
                [
                    '$match' => [
                        'lcdv' => [
                            '$in' => LcdvsProvider::getLcdvsFormatArray($lcdv)
                        ]
                    ]
                ],
                //add a field named lcdvLenght dinamically calculated that contains the length of the lcdv
                [
                    '$addFields' => [
                        'lcdvLength' => [
                            '$cond' => [
                                'if' => ['$isArray' => '$lcdv'],
                                'then' => ['$max' => ['$map' => [
                                    'input' => '$lcdv',
                                    'as' => 'lcdvItem',
                                    'in' => ['$strLenCP' => '$$lcdvItem']
                                ]]],
                                'else' => ['$strLenCP' => '$lcdv']
                            ]
                        ]
                    ]
                ],
                // apply a descending sort on the lcdvLength field
                [
                    '$sort' => [
                        'lcdvLength' => -1
                    ]
                ],
                // remove the lcdvLength field from the final result
                [
                    '$project' => [
                        'lcdvLength' => 0
                    ]
                ],
                // limit the result to 1 document
                [
                    '$limit' => 1
                ],
                //$projection
            ]
        );

        $response = json_decode($response->getData(), true)['documents'][0] ?? [];
        if(isset($response['visualSettings']))
        {

            $response['visualSettings']['visual'] = $this->visualSettings[$brand]['baseUrl'];

            if(!isset($response['visualSettings']['width']) || empty($response['visualSettings']['width'])){
                $response['visualSettings']['width'] = $this->visualSettings['width'];
            }

            if(!isset($response['visualSettings']['height']) || empty($response['visualSettings']['height'])){
                $response['visualSettings']['height'] = $this->visualSettings['height'];
            }
        }

        return $response;
        */
    }
}

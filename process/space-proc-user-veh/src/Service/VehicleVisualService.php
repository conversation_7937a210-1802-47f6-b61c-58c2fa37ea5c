<?php

namespace App\Service;

use App\Helper\LcdvsProvider;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\VehicleLabel;
use Space\MongoDocuments\Service\MongoDBService;

class VehicleVisualService
{
    use LoggerTrait;

    public function __construct(
        private MongoDBService $mongoDBService,
        private array $visualSettings
    ) {
    }

    /**
     * get visual vehicle from BO (v3D or default image..)
     */
    public function getVisualVehicle(?string $lcdv, string $brand): array
    {
        $this->logger->info('Get LVH Label', ['lcdv' => $lcdv, 'brand' => $brand]);

        try {
            if (empty($lcdv)) {
                $this->logger->info('No LCDV provided, returning default visual settings');
                return $this->visualSettings;
            }

            // Use the VehicleLabelRepository to find by LCDV with longest match
            $vehicleLabel = $this->mongoDBService->getRepository(VehicleLabel::class)
                ->findByLcdvWithLongestMatch($lcdv);

            if (!$vehicleLabel) {
                $this->logger->info('No vehicle label found for LCDV, returning default visual settings', ['lcdv' => $lcdv]);
                return $this->visualSettings;
            }

            $visualSettings = $vehicleLabel->getVisualSettings();

            if (!$visualSettings) {
                $this->logger->info('No visual settings found in vehicle label, returning default visual settings', ['lcdv' => $lcdv]);
                return $this->visualSettings;
            }

            // Apply brand-specific visual URL and default width/height if missing
            $visualSettings['visual'] = $this->visualSettings[$brand]['baseUrl'] ?? '';

            if (!isset($visualSettings['width']) || empty($visualSettings['width'])) {
                $visualSettings['width'] = $this->visualSettings['width'];
            }

            if (!isset($visualSettings['height']) || empty($visualSettings['height'])) {
                $visualSettings['height'] = $this->visualSettings['height'];
            }

            $this->logger->info('Successfully retrieved visual settings', ['lcdv' => $lcdv, 'brand' => $brand]);
            return ['visualSettings' => $visualSettings];

        } catch (\Exception $e) {
            $this->logger->error('Error getting visual vehicle', [
                'lcdv' => $lcdv,
                'brand' => $brand,
                'exception' => $e->getMessage()
            ]);
            return $this->visualSettings;
        }


    }
}

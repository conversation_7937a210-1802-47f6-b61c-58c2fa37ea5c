<?php

namespace App\Service;

use App\Connector\SystemOmniConnector;
use App\Helper\BrandHelper;
use App\Helper\WSResponse;
use App\Helper\SuccessResponse;
use App\Helper\ErrorResponse;
use App\Model\VehicleModel;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Service\MongoDBService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Uid\Uuid;

/**
 * Vehicle service - Migrated to use Doctrine ODM instead of MongoDB Atlas API
 * Following Manager→Service→External Service pattern
 */
class VehicleService
{
    use LoggerTrait;

    public const COLLECTION = 'userData';
    public const CREATION_STATUS = 'CREATION';

    private VehicleModel $vehicleModel;

    public function __construct(
        private UserDataService $userDataService,
        private MongoDBService $mongoDBService,
        private SerializerInterface $serializer,
        private SystemOmniConnector $sysOmniConnector,
        private CorvetService $corvetService,
        private BrandHelper $brandHelper
    ) {
    }

    /**
     * Get all vehicles on order for a user.
     * Migrated to use ODM instead of MongoDB Atlas API
     */
    public function getVehiclesOnOrder(string $userId): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicles on order for user', [
                'userId' => $userId,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' User not found', [
                    'userId' => $userId,
                ]);
                return new WSResponse(Response::HTTP_NOT_FOUND, json_encode(['documents' => []]));
            }

            $vehicles = $userData->getVehicles();
            $vehiclesOnOrder = [];

            foreach ($vehicles as $vehicle) {
                // Check if vehicle is on order (using status field as proxy for isOrder)
                if ($vehicle->getStatus() === 'ORDERED' || $this->isVehicleOnOrder($vehicle)) {
                    $vehiclesOnOrder[] = $this->convertVehicleToArray($vehicle);
                }
            }

            $result = [
                'documents' => [
                    [
                        '_id' => 'vehiclesOnOrder',
                        'vehicle' => $vehiclesOnOrder
                    ]
                ]
            ];

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Found vehicles on order', [
                'userId' => $userId,
                'vehicleCount' => count($vehiclesOnOrder),
            ]);

            return new WSResponse(Response::HTTP_OK, json_encode($result));
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting vehicles on order', [
                'userId' => $userId,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode(['error' => 'Error retrieving vehicles on order']));
        }
    }

    /**
     * Mark orders as seen by updating field isUpdated to false.
     * Migrated to use UserDataService ODM implementation
     */
    public function setOrderIsUpdated(string $userId, string $vehicleId, bool $isUpdated): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Setting order updated flag', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isUpdated' => $isUpdated,
            ]);

            $result = $this->userDataService->setOrderIsUpdated($userId, $vehicleId, $isUpdated);

            if ($result) {
                return new WSResponse(Response::HTTP_OK, json_encode(['success' => true]));
            } else {
                return new WSResponse(Response::HTTP_NOT_FOUND, json_encode(['error' => 'Vehicle not found or update failed']));
            }
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error setting order updated flag', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isUpdated' => $isUpdated,
                'error' => $e->getMessage(),
            ]);

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode(['error' => 'Error updating order status']));
        }
    }

    /**
     * Mark orders as seen by updating field isUpdated to false.
     * Migrated to use UserDataService ODM implementation
     */
    public function setOrderIsUpdated(string $userId, string $vehicleId, bool $isUpdated): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Setting order updated flag', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isUpdated' => $isUpdated,
            ]);

            $result = $this->userDataService->setOrderIsUpdated($userId, $vehicleId, $isUpdated);

            if ($result) {
                return new WSResponse(Response::HTTP_OK, json_encode(['success' => true]));
            } else {
                return new WSResponse(Response::HTTP_NOT_FOUND, json_encode(['error' => 'Vehicle not found or update failed']));
            }
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error setting order updated flag', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isUpdated' => $isUpdated,
                'error' => $e->getMessage(),
            ]);

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode(['error' => 'Error updating order status']));
        }
    }

    /**
     * Helper method to check if a vehicle is on order
     */
    private function isVehicleOnOrder(Vehicle $vehicle): bool
    {
        // Check if vehicle has isOrder flag set to true
        if ($vehicle->getIsOrder() === true) {
            return true;
        }

        // Check if vehicle has order data (vehicleOrder with mopId or orderFormId)
        $vehicleOrder = $vehicle->getVehicleOrder();
        if ($vehicleOrder && (!empty($vehicleOrder->getMopId()) || !empty($vehicleOrder->getOrderFormId()))) {
            return true;
        }

        // Check status field as fallback
        return $vehicle->getStatus() === 'ORDERED';
    }

    /**
     * Convert Vehicle ODM object to array format for backward compatibility
     */
    private function convertVehicleToArray(Vehicle $vehicle): array
    {
        $vehicleArray = [
            'id' => $vehicle->getDocumentId(),
            'vin' => $vehicle->getVin(),
            'brand' => $vehicle->getBrand(),
            'model' => $vehicle->getModel(),
            'versionId' => $vehicle->getVersionId(),
            'label' => $vehicle->getLabel(),
            'status' => $vehicle->getStatus(),
            'isOrder' => $vehicle->getIsOrder(),
        ];

        // Add vehicle order data if present
        $vehicleOrder = $vehicle->getVehicleOrder();
        if ($vehicleOrder) {
            $vehicleArray['vehicleOrder'] = $vehicleOrder->toArray();
        }

        return $vehicleArray;
    }

    /**
     * Build the pipepline to serve the order aggregate request.
     * @deprecated This method is kept for backward compatibility but not used in ODM implementation
     */
    public function getVehicleOrderFilter(string $userId, ?bool $isOrder = true): array
    {
        return
            [
                [
                    '$match' => ['userId' => $userId],
                ],
                [
                    '$unwind' => '$vehicle',
                ],
                [
                    '$match' => [
                        'vehicle.isOrder' => ['$eq' => $isOrder],
                    ],
                ],
                [
                    '$group' => [
                        '_id' => 'vehiclesOnOrder',
                        'vehicle' => [
                            '$push' => '$vehicle',
                        ],
                    ],
                ],
            ];
    }

    /**
     * create or update vehicle function.
     */
    public function createOrUpdateVehicle(string $userId, VehicleModel $vehicleModel): WSResponse
    {
        $dbUserDataId = $this->getUserData($userId);
        $this->vehicleModel = $vehicleModel;
        $this->vehicleModel->setIsOrder(true);
        if (
            'DELIVERED_CUSTOMER' == $vehicleModel->getVehicleOrder()->getTrackingStatus()
            && $this->brandHelper->isXpBrand($vehicleModel->getBrand())
        ) {
            $this->vehicleModel->setIsOrder(false);
            $this->vehicleModel->setAddStatus(self::CREATION_STATUS);
        }
        // if user exist ;
        if (! empty($dbUserDataId)) {
            // update vehicle order
            $vehicle = $this->getVehicleByMop($dbUserDataId);
            if (! empty($vehicle['documents'])) {
                // update vehicle object if we have mopId exist
                $this->vehicleModel->setId($vehicle['documents'][0]['vehicle'][0]['id']);
                return $this->updateVehicle($dbUserDataId);
            }
            // push new vehicle in vehicles object
            return $this->insertVehicle($dbUserDataId);
        }

        // create new user with vehicle object
        return $this->createUserWithVehicle($userId);
    }

    /**
     * create user data and vehicle object.
     * Migrated to use UserDataService ODM implementation
     */
    public function createUserWithVehicle(string $userId): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Creating user with vehicle', [
                'userId' => $userId,
            ]);

            // Set vehicle properties
            $this->vehicleModel->setId($this->getNewUuid());
            $this->vehicleModel->getVehicleOrder()->setIsUpdated(true);
            $this->vehicleModel->setIsOrder(true);

            // Convert vehicle model to array for UserDataService
            $vehicleData = $this->getVehicleArray();

            $result = $this->userDataService->createUserWithVehicle($userId, $vehicleData);

            if ($result) {
                return new WSResponse(Response::HTTP_CREATED, json_encode(['success' => true]));
            } else {
                return new WSResponse(Response::HTTP_BAD_REQUEST, json_encode(['error' => 'Failed to create user with vehicle']));
            }
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error creating user with vehicle', [
                'userId' => $userId,
                'error' => $e->getMessage(),
            ]);

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode(['error' => 'Error creating user with vehicle']));
        }
    }

    /**
     * find vehicle object by MOP.
     * Migrated to use ODM implementation
     */
    public function getVehicleByMop(string $dbUserDataId)
    {
        try {
            $mopId = $this->vehicleModel->getVehicleOrder()->getMopId();

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Finding vehicle by MOP ID', [
                'dbUserDataId' => $dbUserDataId,
                'mopId' => $mopId,
            ]);

            // Find user data by ID
            $userData = $this->mongoDBService->findOneBy('userData', ['_id' => $dbUserDataId]);

            if (!$userData) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' User data not found', [
                    'dbUserDataId' => $dbUserDataId,
                ]);
                return ['documents' => []];
            }

            $matchingVehicles = [];

            // Search through vehicles for matching MOP ID
            if (isset($userData['vehicle']) && is_array($userData['vehicle'])) {
                foreach ($userData['vehicle'] as $vehicle) {
                    if (isset($vehicle['vehicleOrder']['mopId']) && $vehicle['vehicleOrder']['mopId'] === $mopId) {
                        $matchingVehicles[] = $vehicle;
                    }
                }
            }

            $result = [
                'documents' => [
                    [
                        '_id' => 'vehicleByMop',
                        'vehicle' => $matchingVehicles
                    ]
                ]
            ];

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Found vehicles by MOP ID', [
                'dbUserDataId' => $dbUserDataId,
                'mopId' => $mopId,
                'vehicleCount' => count($matchingVehicles),
            ]);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error finding vehicle by MOP ID', [
                'dbUserDataId' => $dbUserDataId,
                'error' => $e->getMessage(),
            ]);

            return ['documents' => []];
        }
    }

    /**
     * update vehicle object.
     * Migrated to use UserDataService ODM implementation
     */
    public function updateVehicle(string $dbUserDataId): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating vehicle', [
                'dbUserDataId' => $dbUserDataId,
                'vehicleId' => $this->vehicleModel->getId(),
            ]);

            // Set vehicle order updated flag
            $this->vehicleModel->getVehicleOrder()->setIsUpdated(true);

            // Get vehicle data to update
            $vehicleData = $this->getVehicleArray();

            $result = $this->userDataService->updateVehicleInUserDocumentById($dbUserDataId, $this->vehicleModel->getId(), $vehicleData);

            if ($result) {
                return new WSResponse(Response::HTTP_OK, json_encode(['success' => true]));
            } else {
                return new WSResponse(Response::HTTP_NOT_FOUND, json_encode(['error' => 'Vehicle not found or update failed']));
            }
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error updating vehicle', [
                'dbUserDataId' => $dbUserDataId,
                'vehicleId' => $this->vehicleModel->getId(),
                'error' => $e->getMessage(),
            ]);

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode(['error' => 'Error updating vehicle']));
        }
    }

    /**
     * get vehicle fields to update with Mongo syntaxe.
     */
    public function getVehicleFieldsToUpdate(): array
    {
        $vehicle = $this->getVehicleArray();
        $exlus = ['id'];
        $fields = [];
        foreach ($vehicle as $key => $value) {
            if (in_array($key, $exlus)) {
                continue;
            }
            if ('vehicleOrder' === $key) {
                foreach ($vehicle['vehicleOrder'] as $keyOrder => $valueOrder) {
                    $fields["vehicle.$.vehicleOrder.$keyOrder"] = $valueOrder;
                }
                continue;
            }
            $fields["vehicle.$.$key"] = $value;
        }

        return $fields;
    }

    /**
     * insert new vehicle object to DB.
     * Migrated to use UserDataService ODM implementation
     */
    public function insertVehicle(string $dbUserDataId): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Inserting vehicle', [
                'dbUserDataId' => $dbUserDataId,
            ]);

            // Set vehicle properties
            $this->vehicleModel->setId($this->getNewUuid());
            $this->vehicleModel->getVehicleOrder()->setIsUpdated(true);

            // Get vehicle data to insert
            $vehicleData = $this->getVehicleArray();

            // Convert dbUserDataId to userId for UserDataService compatibility
            $userData = $this->mongoDBService->findOneBy('userData', ['_id' => $dbUserDataId]);
            if (!$userData) {
                throw new \Exception('User data not found for ID: ' . $dbUserDataId);
            }

            $result = $this->userDataService->addVehicleToUserDocument($userData['userId'], $vehicleData);

            if ($result) {
                return new WSResponse(Response::HTTP_OK, json_encode(['success' => true]));
            } else {
                return new WSResponse(Response::HTTP_NOT_FOUND, json_encode(['error' => 'User not found or vehicle insertion failed']));
            }
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error inserting vehicle', [
                'dbUserDataId' => $dbUserDataId,
                'error' => $e->getMessage(),
            ]);

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode(['error' => 'Error inserting vehicle']));
        }
    }

    /**
     * get user data infos from DB.
     * Migrated to use UserDataService ODM implementation
     */
    public function getUserData(string $userId): string
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting user data ID', [
                'userId' => $userId,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if ($userData) {
                return $userData->getId() ?? '';
            }

            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' User not found', [
                'userId' => $userId,
            ]);

            return '';
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting user data', [
                'userId' => $userId,
                'error' => $e->getMessage(),
            ]);

            return '';
        }
    }

    /**
     * generate new Uuid as like new ID.
     */
    public function getNewUuid(): string
    {
        return (string) Uuid::v4();
    }

    /**
     * get vehicle object like array to insert/update.
     */
    public function getVehicleArray(): array
    {
        $vehicle = json_decode($this->serializer->serialize($this->vehicleModel, 'json'), true);

        return $vehicle;
    }

    /**
     * Get all vehicles summary.
     */
    public function getOrderSummary(string $orderFormId, string $mopId, string $brand, string $country): WSResponse
    {
        try {
            $this->logger->info(__METHOD__ . " for mopId $brand/$country/$mopId");

            return $this->sysOmniConnector->call(Request::METHOD_GET, "/v1/orders/{$orderFormId}/summary", [
                'query' => [
                    'mop' => $mopId,
                    'type' => 'ORD',
                ],
                'headers' => [
                    'brand' => $brand,
                    'country' => $country,
                ],
            ]);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Catched Exception '.$e->getMessage());

            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    /**
     * get vehicle data with GB cases.
     */
    public function getVehicleData(array $vehicle): array
    {
        if ($this->isOVVehicle($vehicle['versionId'])) {
            $allAttributes = $this->getVinAttributes($vehicle['vin'], $vehicle['brand']);
            $brand = $vehicle['brand'] = $this->getVehicleBrand($allAttributes, $vehicle['country']);
            if ('VX' == $brand) {
                $vehicle['language'] = 'en';
                $vehicle['country'] = 'GB';
            }
        }

        return $vehicle;
    }

    /**
     * check if its OP or VX vehicle for GB case.
     */
    public function isOVVehicle(string $versionId): bool
    {
        return 'G' == strtoupper(substr($versionId, 1, 1));
    }

    /**
     * load vehicle from corvet api.
     */
    public function getVinAttributes(string $vin, string $brand): array
    {
        $vehicleData = $this->corvetService->getData($vin, $brand);

        return $vehicleData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
    }

    /**
     * get vehicle from corvet api.
     */
    public function getDataFromCorvet(string $vin, string $brand): array
    {
        return $this->corvetService->getData($vin, $brand);
        /*
        $response = $this->sysCorvetDataConnector->call(Request::METHOD_GET, "/v1/corvet/{$vin}/data", [
            'query' => ['brand' => $brand],
        ]);

        return $response->getData()['success'];
        */
    }

    /**
     * check if its OP or VX vehicle.
     */
    public function getVehicleBrand(?array $attributes = [], ?string $country = null): string
    {
        foreach ($attributes as $attribute) {
            if ('DZZ' == substr($attribute, 0, 3)) {
                $rest = substr($attribute, 3, 2);
                if ('0V' === $rest) {
                    return 'VX';
                } elseif ('01' === $rest) {
                    return 'OP';
                }
            }
        }

        return (isset($country) && 'GB' === $country) ? 'VX' : 'OP';
    }

    public function updateVehicleIsOrderFlag(string $userId, string $vehicleId, bool $isOrder): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating vehicle isOrder flag', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isOrder' => $isOrder,
            ]);

            $result = $this->userDataService->updateVehicleIsOrderFlag($userId, $vehicleId, $isOrder);

            if ($result) {
                return new WSResponse(Response::HTTP_OK, json_encode(['success' => true]));
            } else {
                return new WSResponse(Response::HTTP_NOT_FOUND, json_encode(['error' => 'Vehicle not found or update failed']));
            }
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error updating vehicle isOrder flag', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isOrder' => $isOrder,
                'error' => $e->getMessage(),
            ]);

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode(['error' => 'Error updating vehicle order flag']));
        }
    }
}
